import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import KernelDensity
from scipy.special import logsumexp

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def simple_kde(data, bandwidth=0.1):
    """简化的KDE"""
    kde = KernelDensity(kernel='gaussian', bandwidth=bandwidth)
    kde.fit(data.reshape(-1, 1))
    return kde

def simple_js_divergence(kde1, kde2, n_samples=5000):
    """简化的JS散度计算"""
    # 从两个分布采样
    samples1 = kde1.sample(n_samples)
    samples2 = kde2.sample(n_samples)
    
    # 计算对数概率
    log_p1_on_s1 = kde1.score_samples(samples1)
    log_p2_on_s2 = kde2.score_samples(samples2)
    
    # 计算混合分布的对数概率
    log_p1_on_s2 = kde1.score_samples(samples2)
    log_p2_on_s1 = kde2.score_samples(samples1)
    
    # JS散度计算
    log_m_on_s1 = logsumexp([log_p1_on_s1 - np.log(2), log_p2_on_s1 - np.log(2)], axis=0)
    log_m_on_s2 = logsumexp([log_p1_on_s2 - np.log(2), log_p2_on_s2 - np.log(2)], axis=0)
    
    js1 = np.mean(log_p1_on_s1 - log_m_on_s1)
    js2 = np.mean(log_p2_on_s2 - log_m_on_s2)
    
    js = 0.5 * (js1 + js2)
    return max(js / np.log(2), 0.0)

def create_mixture(means, stds, weights, n_samples):
    """创建混合分布"""
    weights = np.array(weights) / np.sum(weights)
    component_sizes = np.random.multinomial(n_samples, weights)
    
    samples = []
    for mean, std, size in zip(means, stds, component_sizes):
        if size > 0:
            samples.extend(np.random.normal(mean, std, size))
    
    return np.array(samples)

def calculate_cv(data):
    """计算变异系数"""
    mean_val = np.mean(data)
    std_val = np.std(data)
    return std_val / abs(mean_val) if abs(mean_val) > 1e-10 else np.inf

def run_cv_difference_experiment():
    """运行两个分布CV差值对JS散度影响的实验"""
    print("开始探索两个分布CV差值对JS散度的影响...")

    # 参数设置
    base_mean = 5.0
    n_samples = 500
    n_trials = 8

    # CV范围：0.01到0.1
    cv_min, cv_max = 0.01, 0.1

    # 生成CV组合：创建不同的CV差值
    cv_pairs = []
    cv_differences = np.linspace(0.0, 0.09, 10)  # CV差值从0到0.09

    for cv_diff in cv_differences:
        # 对于每个差值，选择一个基准CV，确保两个CV都在范围内
        cv1 = 0.05  # 固定第一个分布的CV
        cv2 = cv1 + cv_diff  # 第二个分布的CV

        # 确保cv2在有效范围内
        if cv2 <= cv_max:
            cv_pairs.append((cv1, cv2, cv_diff))

    results = []

    for cv1, cv2, cv_diff in cv_pairs:
        print(f"测试CV对: ({cv1:.3f}, {cv2:.3f}), 差值: {cv_diff:.3f}")

        js_values = []

        for trial in range(n_trials):
            # 第一个分布
            std1 = cv1 * base_mean
            data1 = np.random.normal(base_mean, std1, n_samples)

            # 第二个分布
            std2 = cv2 * base_mean
            data2 = np.random.normal(base_mean, std2, n_samples)

            # 验证实际CV
            actual_cv1 = calculate_cv(data1)
            actual_cv2 = calculate_cv(data2)
            actual_cv_diff = abs(actual_cv2 - actual_cv1)

            try:
                # 计算JS散度
                kde1 = simple_kde(data1, bandwidth=0.1)
                kde2 = simple_kde(data2, bandwidth=0.1)
                js = simple_js_divergence(kde1, kde2)
                js_values.append(js)
            except Exception as e:
                print(f"    试验 {trial+1} 出错: {e}")
                continue

        if js_values:
            results.append({
                'cv1': cv1,
                'cv2': cv2,
                'cv_diff': cv_diff,
                'js_mean': np.mean(js_values),
                'js_std': np.std(js_values),
                'js_min': np.min(js_values),
                'js_max': np.max(js_values),
                'actual_cv_diff': actual_cv_diff
            })
            print(f"    平均JS散度: {np.mean(js_values):.4f} ± {np.std(js_values):.4f}")

    return results

def plot_cv_difference_results(results):
    """绘制CV差值实验结果"""
    if not results:
        print("没有结果可绘制")
        return

    cv_diffs = [r['cv_diff'] for r in results]
    js_means = [r['js_mean'] for r in results]
    js_stds = [r['js_std'] for r in results]
    cv1s = [r['cv1'] for r in results]
    cv2s = [r['cv2'] for r in results]

    plt.figure(figsize=(16, 12))

    # 图1: CV差值 vs JS散度
    plt.subplot(2, 3, 1)
    plt.plot(cv_diffs, js_means, 'bo-', linewidth=2, markersize=6, label='平均JS散度')
    plt.fill_between(cv_diffs,
                     [m - s for m, s in zip(js_means, js_stds)],
                     [m + s for m, s in zip(js_means, js_stds)],
                     alpha=0.3, label='±1标准差')
    plt.xlabel('CV差值 |CV₂ - CV₁|')
    plt.ylabel('JS散度')
    plt.title('CV差值与JS散度的关系')
    plt.grid(True, alpha=0.3)
    plt.legend()

    # 图2: 散点图 - CV差值 vs JS散度，颜色表示稳定性
    plt.subplot(2, 3, 2)
    scatter = plt.scatter(cv_diffs, js_means, c=js_stds, cmap='viridis', s=80, alpha=0.7)
    plt.colorbar(scatter, label='JS散度标准差')
    plt.xlabel('CV差值 |CV₂ - CV₁|')
    plt.ylabel('JS散度')
    plt.title('散点图：颜色表示稳定性')
    plt.grid(True, alpha=0.3)

    # 图3: CV1 vs CV2 热力图风格
    plt.subplot(2, 3, 3)
    for i, r in enumerate(results):
        plt.scatter(r['cv1'], r['cv2'], c=r['js_mean'], cmap='plasma', s=100, alpha=0.8)
    plt.colorbar(label='JS散度')
    plt.xlabel('CV₁')
    plt.ylabel('CV₂')
    plt.title('CV组合与JS散度热力图')
    plt.grid(True, alpha=0.3)

    # 图4: JS散度随CV差值的变化趋势
    plt.subplot(2, 3, 4)
    plt.plot(cv_diffs, js_means, 'ro-', linewidth=2, markersize=6)
    plt.xlabel('CV差值')
    plt.ylabel('JS散度')
    plt.title('JS散度变化趋势')
    plt.grid(True, alpha=0.3)

    # 图5: 误差条图
    plt.subplot(2, 3, 5)
    plt.errorbar(cv_diffs, js_means, yerr=js_stds, fmt='go-', linewidth=2,
                markersize=6, capsize=5, capthick=2)
    plt.xlabel('CV差值')
    plt.ylabel('JS散度')
    plt.title('JS散度误差条图')
    plt.grid(True, alpha=0.3)

    # 图6: 数据表
    plt.subplot(2, 3, 6)
    plt.axis('off')

    table_data = []
    for r in results:
        table_data.append([
            f"{r['cv1']:.3f}",
            f"{r['cv2']:.3f}",
            f"{r['cv_diff']:.3f}",
            f"{r['js_mean']:.4f}",
            f"{r['js_std']:.4f}"
        ])

    table = plt.table(cellText=table_data,
                     colLabels=['CV₁', 'CV₂', 'CV差值', 'JS散度', '标准差'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(8)
    table.scale(1.0, 1.5)

    plt.suptitle('两个分布CV差值对JS散度影响的综合分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('cv_difference_js_analysis.png', dpi=300, bbox_inches='tight')
    print("图片已保存为: cv_difference_js_analysis.png")
    plt.show()

if __name__ == '__main__':
    print("两个分布CV差值对JS散度影响分析")
    print("=" * 50)

    # 运行实验
    results = run_cv_difference_experiment()

    # 绘制结果
    plot_cv_difference_results(results)

    # 详细分析
    if results:
        cv_diffs = [r['cv_diff'] for r in results]
        js_means = [r['js_mean'] for r in results]
        js_stds = [r['js_std'] for r in results]

        max_js_idx = np.argmax(js_means)
        min_js_idx = np.argmin(js_means)

        print(f"\n详细分析结果:")
        print("=" * 50)
        print(f"最大JS散度:")
        print(f"  CV差值: {cv_diffs[max_js_idx]:.3f}")
        print(f"  CV对: ({results[max_js_idx]['cv1']:.3f}, {results[max_js_idx]['cv2']:.3f})")
        print(f"  JS散度: {js_means[max_js_idx]:.4f} ± {js_stds[max_js_idx]:.4f}")

        print(f"\n最小JS散度:")
        print(f"  CV差值: {cv_diffs[min_js_idx]:.3f}")
        print(f"  CV对: ({results[min_js_idx]['cv1']:.3f}, {results[min_js_idx]['cv2']:.3f})")
        print(f"  JS散度: {js_means[min_js_idx]:.4f} ± {js_stds[min_js_idx]:.4f}")

        # 相关性分析
        if len(cv_diffs) > 2:
            corr = np.corrcoef(cv_diffs, js_means)[0, 1]
            print(f"\nCV差值与JS散度相关系数: {corr:.4f}")

            if corr > 0.7:
                print("- CV差值与JS散度呈强正相关")
            elif corr > 0.3:
                print("- CV差值与JS散度呈中等正相关")
            elif corr < -0.3:
                print("- CV差值与JS散度呈负相关")
            else:
                print("- CV差值与JS散度相关性较弱")

        # 趋势分析
        print(f"\n趋势分析:")
        if len(js_means) >= 3:
            # 检查是否单调递增
            is_increasing = all(js_means[i] <= js_means[i+1] for i in range(len(js_means)-1))
            # 检查是否单调递减
            is_decreasing = all(js_means[i] >= js_means[i+1] for i in range(len(js_means)-1))

            if is_increasing:
                print("- JS散度随CV差值单调递增")
            elif is_decreasing:
                print("- JS散度随CV差值单调递减")
            else:
                print("- JS散度随CV差值呈非单调变化")

        # 数值总结
        print(f"\n数值总结:")
        print(f"- CV差值范围: {min(cv_diffs):.3f} - {max(cv_diffs):.3f}")
        print(f"- JS散度范围: {min(js_means):.4f} - {max(js_means):.4f}")
        print(f"- 平均JS散度: {np.mean(js_means):.4f}")
        print(f"- JS散度变化幅度: {max(js_means) - min(js_means):.4f}")

    print("\n实验完成！图片已保存到当前目录。")
