import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import KernelDensity
from scipy.special import logsumexp

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def simple_kde(data, bandwidth=0.1):
    """简化的KDE"""
    kde = KernelDensity(kernel='gaussian', bandwidth=bandwidth)
    kde.fit(data.reshape(-1, 1))
    return kde

def simple_js_divergence(kde1, kde2, n_samples=5000):
    """简化的JS散度计算"""
    # 从两个分布采样
    samples1 = kde1.sample(n_samples)
    samples2 = kde2.sample(n_samples)
    
    # 计算对数概率
    log_p1_on_s1 = kde1.score_samples(samples1)
    log_p2_on_s2 = kde2.score_samples(samples2)
    
    # 计算混合分布的对数概率
    log_p1_on_s2 = kde1.score_samples(samples2)
    log_p2_on_s1 = kde2.score_samples(samples1)
    
    # JS散度计算
    log_m_on_s1 = logsumexp([log_p1_on_s1 - np.log(2), log_p2_on_s1 - np.log(2)], axis=0)
    log_m_on_s2 = logsumexp([log_p1_on_s2 - np.log(2), log_p2_on_s2 - np.log(2)], axis=0)
    
    js1 = np.mean(log_p1_on_s1 - log_m_on_s1)
    js2 = np.mean(log_p2_on_s2 - log_m_on_s2)
    
    js = 0.5 * (js1 + js2)
    return max(js / np.log(2), 0.0)

def create_mixture(means, stds, weights, n_samples):
    """创建混合分布"""
    weights = np.array(weights) / np.sum(weights)
    component_sizes = np.random.multinomial(n_samples, weights)
    
    samples = []
    for mean, std, size in zip(means, stds, component_sizes):
        if size > 0:
            samples.extend(np.random.normal(mean, std, size))
    
    return np.array(samples)

def calculate_cv(data):
    """计算变异系数"""
    mean_val = np.mean(data)
    std_val = np.std(data)
    return std_val / abs(mean_val) if abs(mean_val) > 1e-10 else np.inf

def run_cv_experiment():
    """运行CV实验"""
    print("开始CV与JS散度实验...")
    
    # 参数设置
    base_mean = 5.0
    base_cv = 0.2
    base_std = base_cv * base_mean
    n_samples = 500
    n_trials = 5
    
    # CV范围
    cvs = np.linspace(0.1, 1.0, 10)
    
    results = []
    
    for cv in cvs:
        print(f"测试CV: {cv:.2f}")
        
        js_values = []
        
        for trial in range(n_trials):
            # 基准分布
            base_data = np.random.normal(base_mean, base_std, n_samples)
            
            # 混合分布 - 双峰设计
            target_std = cv * base_mean
            offset = target_std * 0.6
            
            mixture_data = create_mixture(
                means=[base_mean - offset, base_mean + offset],
                stds=[target_std * 0.4, target_std * 0.4],
                weights=[0.5, 0.5],
                n_samples=n_samples
            )
            
            # 计算实际CV
            actual_cv = calculate_cv(mixture_data)
            
            try:
                # 计算JS散度
                kde_base = simple_kde(base_data, bandwidth=0.2)
                kde_mixture = simple_kde(mixture_data, bandwidth=0.2)
                js = simple_js_divergence(kde_base, kde_mixture)
                js_values.append(js)
            except:
                continue
        
        if js_values:
            results.append({
                'cv': cv,
                'js_mean': np.mean(js_values),
                'js_std': np.std(js_values),
                'actual_cv': actual_cv
            })
    
    return results

def plot_results(results):
    """绘制结果"""
    if not results:
        print("没有结果可绘制")
        return
    
    cvs = [r['cv'] for r in results]
    js_means = [r['js_mean'] for r in results]
    js_stds = [r['js_std'] for r in results]
    
    plt.figure(figsize=(12, 8))
    
    # 主图
    plt.subplot(2, 2, 1)
    plt.plot(cvs, js_means, 'bo-', linewidth=2, markersize=6)
    plt.fill_between(cvs, 
                     [m - s for m, s in zip(js_means, js_stds)], 
                     [m + s for m, s in zip(js_means, js_stds)], 
                     alpha=0.3)
    plt.xlabel('目标变异系数 (CV)')
    plt.ylabel('JS散度')
    plt.title('变异系数与JS散度的关系')
    plt.grid(True, alpha=0.3)
    
    # 散点图
    plt.subplot(2, 2, 2)
    plt.scatter(cvs, js_means, c=js_stds, cmap='viridis', s=80)
    plt.colorbar(label='JS散度标准差')
    plt.xlabel('目标变异系数 (CV)')
    plt.ylabel('JS散度')
    plt.title('散点图：颜色表示稳定性')
    plt.grid(True, alpha=0.3)
    
    # 数据表
    plt.subplot(2, 1, 2)
    plt.axis('off')
    
    table_data = []
    for r in results[::2]:  # 每隔一个显示
        table_data.append([
            f"{r['cv']:.2f}",
            f"{r['js_mean']:.4f}",
            f"{r['js_std']:.4f}"
        ])
    
    table = plt.table(cellText=table_data,
                     colLabels=['CV', 'JS散度均值', 'JS散度标准差'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1.2, 2)
    
    plt.suptitle('混合分布变异系数对JS散度影响分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('cv_js_analysis_simple.png', dpi=300, bbox_inches='tight')
    print("图片已保存为: cv_js_analysis_simple.png")
    plt.show()

if __name__ == '__main__':
    print("简化版CV与JS散度分析")
    print("=" * 40)
    
    # 运行实验
    results = run_cv_experiment()
    
    # 绘制结果
    plot_results(results)
    
    # 简单分析
    if results:
        cvs = [r['cv'] for r in results]
        js_means = [r['js_mean'] for r in results]
        
        max_js_idx = np.argmax(js_means)
        min_js_idx = np.argmin(js_means)
        
        print(f"\n分析结果:")
        print(f"最大JS散度: CV={cvs[max_js_idx]:.2f}, JS={js_means[max_js_idx]:.4f}")
        print(f"最小JS散度: CV={cvs[min_js_idx]:.2f}, JS={js_means[min_js_idx]:.4f}")
        
        # 相关性
        corr = np.corrcoef(cvs, js_means)[0, 1]
        print(f"CV与JS散度相关系数: {corr:.3f}")
        
        if corr > 0.5:
            print("CV与JS散度呈正相关")
        elif corr < -0.5:
            print("CV与JS散度呈负相关")
        else:
            print("CV与JS散度相关性较弱")
    
    print("\n实验完成！")
