import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from scipy.integrate import simps
from sklearn.neighbors import KernelDensity
from sklearn.model_selection import GridSearchCV
import warnings
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler,MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap
from scipy.spatial.distance import cdist
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import trustworthiness, TSNE
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import pdist, squareform
from dcor import distance_correlation
import umap
from scipy.special import logsumexp
import os
from alibi_detect.cd import MMDDrift
from datetime import datetime
from datetime import datetime, timedelta
from scipy.stats import ks_2samp
from scipy.stats import skewnorm

# 设置中文字体 (请确保您的系统有该字体)
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei'
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"中文字体设置失败: {e}")

def optimized_joint_kde(data, n, n_folds=3):
    """
    优化带宽的多维核密度估计
    参数：
        data : 数据矩阵，形状为(n_samples, n)
        n : 数据维度
        n_folds : 交叉验证折数（默认3折平衡精度与速度）
    返回：
        kde : 优化带宽后的KDE模型
    """
    # 数据维度校验
    assert data.shape[1] == n, f"输入数据维度应为{n}维，当前维度{data.shape[1]}"
    
    n_samples, d = data.shape
    
    # 计算所有维度的标准差，并取平均
    std_dev = np.mean(np.std(data, axis=0))
    
    # 分位数差估计 s*
    q977 = np.percentile(data, 97.7, axis=0)
    q023 = np.percentile(data, 2.3, axis=0)
    s_star = np.mean((q977 - q023) / 4)  # 平均化多维情况
    
    # 选择 min(σ̂, s*) 并计算带宽
    sigma_star = min(std_dev, s_star)
    h_m_star = 1.03 * sigma_star * (n_samples ** (-1 / (d + 4)))
    
    # 构建动态搜索网格
    bandwidth_grid = np.linspace(h_m_star*0.5, h_m_star*2, 5)
    
    # 交叉验证网格搜索
    grid = GridSearchCV(KernelDensity(kernel='gaussian'),
                      {'bandwidth': bandwidth_grid},
                      cv=n_folds,
                      scoring=lambda est, X: est.score(X),
                      n_jobs=-1)
    grid.fit(data)
    return grid.best_estimator_

def compute_divergence(kde_p, kde_q, n_samples=10000):
    """
    严格数学实现的JS散度计算
    参数：
        kde_p : 第一个分布的KDE模型
        kde_q : 第二个分布的KDE模型
        n_samples : 蒙特卡洛采样点数(建议>=1e5)
    返回：
        js_divergence
    """
    # ================== JS散度计算 ==================
    # 从P分布采样
    samples_p = kde_p.sample(n_samples)
    log_p_p = kde_p.score_samples(samples_p)

    # 从Q分布采样
    samples_q = kde_q.sample(n_samples)
    log_q_q = kde_q.score_samples(samples_q)
    # 混合分布M计算
    def calc_log_m(samples):
        log_p = kde_p.score_samples(samples)
        log_q = kde_q.score_samples(samples)
        return logsumexp([log_p - np.log(2), log_q - np.log(2)], axis=0)

    # 计算JS散度
    log_m_p = calc_log_m(samples_p)
    log_m_q = calc_log_m(samples_q)

    js_p = np.mean(log_p_p - log_m_p)
    js_q = np.mean(log_q_q - log_m_q)
    js = 0.5 * (js_p + js_q)
    js = max(js, 0.0)  # 确保非负
    
    # 范围[0, 1]
    return js/ np.log(2) 



def run_js_mean_sensitivity_experiment(mean_a, mean_b, variance=1.0, n_samples=1000, n_trials=100, n_dim=1):
    """
    执行JS散度对均值差异敏感性的重复实验。

    该函数生成两个不同均值的正态分布样本，
    然后计算它们之间的JS散度。重复此过程多次以评估稳定性和均值。

    Args:
        mean_a (float): 第一个分布的均值。
        mean_b (float): 第二个分布的均值。
        variance (float): 固定的方差值，默认为1.0。
        n_samples (int): 每个分布的样本数量。
        n_trials (int): 重复实验的次数。
        n_dim (int): 数据的维度。

    Returns:
        dict: 包含JS散度列表和统计摘要的字典。
    """
    print(f"\n--- 开始测试: 均值对比 ({mean_a:.2f} vs {mean_b:.2f}) ---")

    js_divergences = []
    std_dev = np.sqrt(variance)

    for i in range(n_trials):
        # 生成两个不同均值的正态分布样本
        dist_a_samples = np.random.normal(loc=mean_a, scale=std_dev, size=(n_samples, n_dim))
        dist_b_samples = np.random.normal(loc=mean_b, scale=std_dev, size=(n_samples, n_dim))
        
        # 2. 调用您的方法进行KDE和JS散度计算
        try:
            kde_a = optimized_joint_kde(dist_a_samples, n=n_dim)
            kde_b = optimized_joint_kde(dist_b_samples, n=n_dim)
            js = compute_divergence(kde_a, kde_b)
            js_divergences.append(js)
        except Exception as e:
            print(f"  在第 {i+1} 次试验中发生错误: {e}")
            continue
            
    if not js_divergences:
        print("所有试验均失败，无法生成结果。")
        return None

    # 计算统计摘要
    stats = {
        'mean': np.mean(js_divergences),
        'std': np.std(js_divergences),
        'min': np.min(js_divergences),
        'max': np.max(js_divergences)
    }
    
    print(f"完成 {len(js_divergences)}/{n_trials} 次有效试验。")
    print(f"  - 平均JS散度: {stats['mean']:.4f}")
    print(f"  - JS散度标准差: {stats['std']:.4f} (越小越稳定)")
    print(f"  - 范围: [{stats['min']:.4f}, {stats['max']:.4f}]")

    return {
        'values': js_divergences,
        'stats': stats
    }

def run_js_variance_sensitivity_experiment(variance_a, variance_b, mean=0.0, n_samples=1000, n_trials=100, n_dim=1):
    """
    执行JS散度对方差差异敏感性的重复实验。

    该函数生成两个不同方差的正态分布样本，
    然后计算它们之间的JS散度。重复此过程多次以评估稳定性和均值。

    Args:
        variance_a (float): 第一个分布的方差。
        variance_b (float): 第二个分布的方差。
        mean (float): 固定的均值，默认为0.0。
        n_samples (int): 每个分布的样本数量。
        n_trials (int): 重复实验的次数。
        n_dim (int): 数据的维度。

    Returns:
        dict: 包含JS散度列表和统计摘要的字典。
    """
    print(f"\n--- 开始测试: 方差对比 ({variance_a:.2f} vs {variance_b:.2f}) ---")

    js_divergences = []
    std_dev_a = np.sqrt(variance_a)
    std_dev_b = np.sqrt(variance_b)

    for i in range(n_trials):
        # 生成两个不同方差的正态分布样本
        dist_a_samples = np.random.normal(loc=mean, scale=std_dev_a, size=(n_samples, n_dim))
        dist_b_samples = np.random.normal(loc=mean, scale=std_dev_b, size=(n_samples, n_dim))

        # 调用KDE和JS散度计算
        try:
            kde_a = optimized_joint_kde(dist_a_samples, n=n_dim)
            kde_b = optimized_joint_kde(dist_b_samples, n=n_dim)
            js = compute_divergence(kde_a, kde_b)
            js_divergences.append(js)
        except Exception as e:
            print(f"  在第 {i+1} 次试验中发生错误: {e}")
            continue

    if not js_divergences:
        print("所有试验均失败，无法生成结果。")
        return None

    # 计算统计摘要
    stats = {
        'mean': np.mean(js_divergences),
        'std': np.std(js_divergences),
        'min': np.min(js_divergences),
        'max': np.max(js_divergences)
    }

    print(f"完成 {len(js_divergences)}/{n_trials} 次有效试验。")
    print(f"  - 平均JS散度: {stats['mean']:.4f}")
    print(f"  - JS散度标准差: {stats['std']:.4f} (越小越稳定)")
    print(f"  - 范围: [{stats['min']:.4f}, {stats['max']:.4f}]")

    return {
        'values': js_divergences,
        'stats': stats
    }

def plot_mean_js_relationship(all_results, save_path="mean_js_analysis.png"):
    """
    绘制均值差异与JS散度关系的综合图表
    """
    mean_pairs = sorted(all_results.keys())
    js_means = [all_results[pair]['mean'] for pair in mean_pairs]
    js_stds = [all_results[pair]['std'] for pair in mean_pairs]

    # 计算均值差异（绝对值）
    mean_diffs = [abs(pair[0] - pair[1]) for pair in mean_pairs]

    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('不同均值对JS散度影响的综合分析', fontsize=16, fontweight='bold')

    # 图1: 均值差异 vs 平均JS散度
    ax1.plot(mean_diffs, js_means, 'bo-', linewidth=2, markersize=8, label='平均JS散度')
    ax1.fill_between(mean_diffs,
                     [m - s for m, s in zip(js_means, js_stds)],
                     [m + s for m, s in zip(js_means, js_stds)],
                     alpha=0.3, label='±1标准差')
    ax1.set_xlabel('均值差异 |μ₁ - μ₂|')
    ax1.set_ylabel('平均JS散度')
    ax1.set_title('均值差异与平均JS散度的关系')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 图2: 均值差异 vs JS散度标准差（稳定性）
    ax2.plot(mean_diffs, js_stds, 'ro-', linewidth=2, markersize=8, label='JS散度标准差')
    ax2.set_xlabel('均值差异 |μ₁ - μ₂|')
    ax2.set_ylabel('JS散度标准差')
    ax2.set_title('均值差异与JS散度稳定性的关系')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 图3: 散点图 - 均值差异 vs 平均JS散度
    scatter = ax3.scatter(mean_diffs, js_means, c=js_stds, cmap='viridis', s=100, alpha=0.7)
    ax3.set_xlabel('均值差异 |μ₁ - μ₂|')
    ax3.set_ylabel('平均JS散度')
    ax3.set_title('散点图：颜色表示稳定性')
    cbar = plt.colorbar(scatter, ax=ax3)
    cbar.set_label('JS散度标准差')
    ax3.grid(True, alpha=0.3)

    # 图4: 热力图风格的数据表
    ax4.axis('tight')
    ax4.axis('off')
    table_data = []
    for i, pair in enumerate(mean_pairs):
        stats = all_results[pair]
        table_data.append([f'({pair[0]:.1f}, {pair[1]:.1f})', f'{mean_diffs[i]:.2f}',
                          f'{stats["mean"]:.4f}', f'{stats["std"]:.4f}',
                          f'{stats["min"]:.4f}', f'{stats["max"]:.4f}'])

    table = ax4.table(cellText=table_data,
                     colLabels=['均值对(μ₁,μ₂)', '均值差异', '平均JS散度', '标准差', '最小值', '最大值'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 1.5)
    ax4.set_title('详细统计数据表')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"均值分析图已保存到: {save_path}")
    plt.show()

def plot_variance_js_relationship(all_results, save_path="variance_js_analysis.png"):
    """
    绘制方差差异与JS散度关系的综合图表
    """
    variance_pairs = sorted(all_results.keys())
    js_means = [all_results[pair]['mean'] for pair in variance_pairs]
    js_stds = [all_results[pair]['std'] for pair in variance_pairs]

    # 计算方差比值（较大方差/较小方差）
    variance_ratios = [max(pair[0], pair[1]) / min(pair[0], pair[1]) for pair in variance_pairs]

    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('不同方差对JS散度影响的综合分析', fontsize=16, fontweight='bold')

    # 图1: 方差比值 vs 平均JS散度
    ax1.plot(variance_ratios, js_means, 'bo-', linewidth=2, markersize=8, label='平均JS散度')
    ax1.fill_between(variance_ratios,
                     [m - s for m, s in zip(js_means, js_stds)],
                     [m + s for m, s in zip(js_means, js_stds)],
                     alpha=0.3, label='±1标准差')
    ax1.set_xlabel('方差比值 (σ²max / σ²min)')
    ax1.set_ylabel('平均JS散度')
    ax1.set_title('方差比值与平均JS散度的关系')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 图2: 方差比值 vs JS散度标准差（稳定性）
    ax2.plot(variance_ratios, js_stds, 'ro-', linewidth=2, markersize=8, label='JS散度标准差')
    ax2.set_xlabel('方差比值 (σ²max / σ²min)')
    ax2.set_ylabel('JS散度标准差')
    ax2.set_title('方差比值与JS散度稳定性的关系')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 图3: 散点图 - 方差比值 vs 平均JS散度
    scatter = ax3.scatter(variance_ratios, js_means, c=js_stds, cmap='viridis', s=100, alpha=0.7)
    ax3.set_xlabel('方差比值 (σ²max / σ²min)')
    ax3.set_ylabel('平均JS散度')
    ax3.set_title('散点图：颜色表示稳定性')
    cbar = plt.colorbar(scatter, ax=ax3)
    cbar.set_label('JS散度标准差')
    ax3.grid(True, alpha=0.3)

    # 图4: 热力图风格的数据表
    ax4.axis('tight')
    ax4.axis('off')
    table_data = []
    for i, pair in enumerate(variance_pairs):
        stats = all_results[pair]
        table_data.append([f'({pair[0]:.1f}, {pair[1]:.1f})', f'{variance_ratios[i]:.2f}',
                          f'{stats["mean"]:.4f}', f'{stats["std"]:.4f}',
                          f'{stats["min"]:.4f}', f'{stats["max"]:.4f}'])

    table = ax4.table(cellText=table_data,
                     colLabels=['方差对(σ₁²,σ₂²)', '方差比值', '平均JS散度', '标准差', '最小值', '最大值'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 1.5)
    ax4.set_title('详细统计数据表')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"方差分析图已保存到: {save_path}")
    plt.show()

def run_mean_experiment():
    """运行均值敏感性实验"""
    print("=" * 80)
    print("                    均值敏感性实验")
    print("=" * 80)
    print("固定方差 σ² = 1.0，测试不同均值对JS散度的影响")

    # 定义要测试的均值对比组合
    mean_pairs_to_test = [
        (0.0, 0.0),    # 基线：相同均值
        (0.0, 0.5),    # 小差异
        (0.0, 1.0),    # 中等差异
        (0.0, 1.5),    # 较大差异
        (0.0, 2.0),    # 大差异
        (0.0, 3.0),    # 很大差异
        (-1.0, 1.0),   # 对称差异
        (-2.0, 2.0),   # 大对称差异
    ]

    all_results = {}

    for mean_a, mean_b in mean_pairs_to_test:
        result = run_js_mean_sensitivity_experiment(
            mean_a=mean_a,
            mean_b=mean_b,
            variance=1.0,   # 固定方差为1
            n_samples=500,  # 样本量
            n_trials=15,    # 试验次数
            n_dim=1         # 一维特征
        )
        if result:
            all_results[(mean_a, mean_b)] = result['stats']

    # 结果汇总
    print("\n均值敏感性实验总结:")
    print("均值对(μ₁,μ₂) | 均值差异 | 平均JS散度 | JS散度标准差 | 最小值   | 最大值")
    print("------------|----------|------------|-------------|----------|----------")
    for pair in sorted(all_results.keys(), key=lambda x: abs(x[0] - x[1])):
        stats = all_results[pair]
        mean_diff = abs(pair[0] - pair[1])
        print(f"  ({pair[0]:4.1f},{pair[1]:4.1f}) |   {mean_diff:5.2f}   |   {stats['mean']:.4f}   |    {stats['std']:.4f}    |  {stats['min']:.4f}  |  {stats['max']:.4f}")

    # 绘制分析图
    if all_results:
        plot_mean_js_relationship(all_results, "mean_js_analysis.png")

    return all_results

def run_variance_experiment():
    """运行方差敏感性实验"""
    print("\n" + "=" * 80)
    print("                    方差敏感性实验")
    print("=" * 80)
    print("固定均值 μ = 0.0，测试不同方差对JS散度的影响")

    # 定义要测试的方差对比组合
    variance_pairs_to_test = [
        (1.0, 1.0),    # 基线：相同方差
        (1.0, 1.5),    # 小差异
        (1.0, 2.0),    # 中等差异
        (1.0, 3.0),    # 较大差异
        (1.0, 4.0),    # 大差异
        (1.0, 6.0),    # 很大差异
        (0.5, 2.0),    # 4倍差异
        (0.25, 4.0),   # 16倍差异
    ]

    all_results = {}

    for var_a, var_b in variance_pairs_to_test:
        result = run_js_variance_sensitivity_experiment(
            variance_a=var_a,
            variance_b=var_b,
            mean=0.0,       # 固定均值为0
            n_samples=500,  # 样本量
            n_trials=15,    # 试验次数
            n_dim=1         # 一维特征
        )
        if result:
            all_results[(var_a, var_b)] = result['stats']

    # 结果汇总
    print("\n方差敏感性实验总结:")
    print("方差对(σ₁²,σ₂²) | 方差比值 | 平均JS散度 | JS散度标准差 | 最小值   | 最大值")
    print("-------------|----------|------------|-------------|----------|----------")
    for pair in sorted(all_results.keys(), key=lambda x: max(x[0], x[1])/min(x[0], x[1])):
        stats = all_results[pair]
        var_ratio = max(pair[0], pair[1]) / min(pair[0], pair[1])
        print(f"  ({pair[0]:4.1f},{pair[1]:4.1f}) |   {var_ratio:5.2f}   |   {stats['mean']:.4f}   |    {stats['std']:.4f}    |  {stats['min']:.4f}  |  {stats['max']:.4f}")

    # 绘制分析图
    if all_results:
        plot_variance_js_relationship(all_results, "variance_js_analysis.png")

    return all_results

if __name__ == '__main__':
    print("JS散度敏感性分析实验")
    print("本实验分别探索均值和方差对JS散度的影响")

    # 运行均值实验
    mean_results = run_mean_experiment()

    # 运行方差实验
    variance_results = run_variance_experiment()

    # 综合分析
    print("\n" + "=" * 80)
    print("                    综合分析总结")
    print("=" * 80)

    if mean_results:
        # 均值实验分析
        mean_diffs = [abs(pair[0] - pair[1]) for pair in mean_results.keys()]
        mean_js_values = [mean_results[pair]['mean'] for pair in mean_results.keys()]

        if len(mean_diffs) > 2:
            corr_mean = np.corrcoef(mean_diffs, mean_js_values)[0, 1]
            print(f"均值差异与JS散度的相关系数: {corr_mean:.4f}")

            if corr_mean > 0.7:
                print("- 均值差异与JS散度呈强正相关")
            elif corr_mean > 0.3:
                print("- 均值差异与JS散度呈中等正相关")
            else:
                print("- 均值差异与JS散度相关性较弱")

    if variance_results:
        # 方差实验分析
        var_ratios = [max(pair[0], pair[1])/min(pair[0], pair[1]) for pair in variance_results.keys()]
        var_js_values = [variance_results[pair]['mean'] for pair in variance_results.keys()]

        if len(var_ratios) > 2:
            corr_var = np.corrcoef(var_ratios, var_js_values)[0, 1]
            print(f"方差比值与JS散度的相关系数: {corr_var:.4f}")

            if corr_var > 0.7:
                print("- 方差比值与JS散度呈强正相关")
            elif corr_var > 0.3:
                print("- 方差比值与JS散度呈中等正相关")
            else:
                print("- 方差比值与JS散度相关性较弱")

    print("\n实验完成！图片已保存到当前文件夹。")

