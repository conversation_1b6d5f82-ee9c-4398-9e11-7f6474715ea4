import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from scipy.integrate import simps
from sklearn.neighbors import KernelDensity
from sklearn.model_selection import GridSearchCV
import warnings
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler,MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap
from scipy.spatial.distance import cdist
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import trustworthiness, TSNE
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import pdist, squareform
from dcor import distance_correlation
import umap
from scipy.special import logsumexp
import os
from alibi_detect.cd import MMDDrift
from datetime import datetime
from datetime import datetime, timedelta
from scipy.stats import ks_2samp
from scipy.stats import skewnorm

# 设置中文字体 (请确保您的系统有该字体)
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei'
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"中文字体设置失败: {e}")

def optimized_joint_kde(data, n, n_folds=3):
    """
    优化带宽的多维核密度估计
    参数：
        data : 数据矩阵，形状为(n_samples, n)
        n : 数据维度
        n_folds : 交叉验证折数（默认3折平衡精度与速度）
    返回：
        kde : 优化带宽后的KDE模型
    """
    # 数据维度校验
    assert data.shape[1] == n, f"输入数据维度应为{n}维，当前维度{data.shape[1]}"
    
    n_samples, d = data.shape
    
    # 计算所有维度的标准差，并取平均
    std_dev = np.mean(np.std(data, axis=0))
    
    # 分位数差估计 s*
    q977 = np.percentile(data, 97.7, axis=0)
    q023 = np.percentile(data, 2.3, axis=0)
    s_star = np.mean((q977 - q023) / 4)  # 平均化多维情况
    
    # 选择 min(σ̂, s*) 并计算带宽
    sigma_star = min(std_dev, s_star)
    h_m_star = 1.03 * sigma_star * (n_samples ** (-1 / (d + 4)))
    
    # 构建动态搜索网格
    bandwidth_grid = np.linspace(h_m_star*0.5, h_m_star*2, 5)
    
    # 交叉验证网格搜索
    grid = GridSearchCV(KernelDensity(kernel='gaussian'),
                      {'bandwidth': bandwidth_grid},
                      cv=n_folds,
                      scoring=lambda est, X: est.score(X),
                      n_jobs=-1)
    grid.fit(data)
    return grid.best_estimator_

def compute_divergence(kde_p, kde_q, n_samples=10000):
    """
    严格数学实现的JS散度计算
    参数：
        kde_p : 第一个分布的KDE模型
        kde_q : 第二个分布的KDE模型
        n_samples : 蒙特卡洛采样点数(建议>=1e5)
    返回：
        js_divergence
    """
    # ================== JS散度计算 ==================
    # 从P分布采样
    samples_p = kde_p.sample(n_samples)
    log_p_p = kde_p.score_samples(samples_p)

    # 从Q分布采样
    samples_q = kde_q.sample(n_samples)
    log_q_q = kde_q.score_samples(samples_q)
    # 混合分布M计算
    def calc_log_m(samples):
        log_p = kde_p.score_samples(samples)
        log_q = kde_q.score_samples(samples)
        return logsumexp([log_p - np.log(2), log_q - np.log(2)], axis=0)

    # 计算JS散度
    log_m_p = calc_log_m(samples_p)
    log_m_q = calc_log_m(samples_q)

    js_p = np.mean(log_p_p - log_m_p)
    js_q = np.mean(log_q_q - log_m_q)
    js = 0.5 * (js_p + js_q)
    js = max(js, 0.0)  # 确保非负
    
    # 范围[0, 1]
    return js/ np.log(2) 



def run_js_skewness_sensitivity_experiment(skewness_left, skewness_right, variance=1.0, n_samples=1000, n_trials=100, n_dim=2):
    """
    执行JS散度对偏峰分布敏感性的重复实验。

    该函数生成两个不同偏度的偏峰分布样本，
    然后计算它们之间的JS散度。重复此过程多次以评估稳定性和均值。

    Args:
        skewness_left (float): 第一个分布的偏度参数。
        skewness_right (float): 第二个分布的偏度参数。
        variance (float): 固定的方差值，默认为1.0。
        n_samples (int): 每个分布的样本数量。
        n_trials (int): 重复实验的次数。
        n_dim (int): 数据的维度。

    Returns:
        dict: 包含JS散度列表和统计摘要的字典。
    """
    print(f"\n--- 开始测试: 偏度对比 ({skewness_left:.2f} vs {skewness_right:.2f}) ---")

    js_divergences = []
    # 计算标准差，因为 skewnorm 使用 standard deviation (scale)
    std_dev = np.sqrt(variance)

    for i in range(n_trials):
        # 生成两个不同偏度的偏峰分布样本
        # 使用skewnorm生成偏峰分布，loc=0保证均值为0，scale=std_dev控制标准差
        dist_a_samples = skewnorm.rvs(a=skewness_left, loc=0, scale=std_dev, size=(n_samples, n_dim))
        dist_b_samples = skewnorm.rvs(a=skewness_right, loc=0, scale=std_dev, size=(n_samples, n_dim))
        # 对两个分布一起做归一化
        scaler = StandardScaler()
        combined_samples = np.vstack([dist_a_samples, dist_b_samples])
        combined_normalized = scaler.fit_transform(combined_samples)
        dist_a_samples = combined_normalized[:n_samples]
        dist_b_samples = combined_normalized[n_samples:]
        
        # 2. 调用您的方法进行KDE和JS散度计算
        try:
            kde_a = optimized_joint_kde(dist_a_samples, n=n_dim)
            kde_b = optimized_joint_kde(dist_b_samples, n=n_dim)
            js = compute_divergence(kde_a, kde_b)
            js_divergences.append(js)
        except Exception as e:
            print(f"  在第 {i+1} 次试验中发生错误: {e}")
            continue
            
    if not js_divergences:
        print("所有试验均失败，无法生成结果。")
        return None

    # 计算统计摘要
    stats = {
        'mean': np.mean(js_divergences),
        'std': np.std(js_divergences),
        'min': np.min(js_divergences),
        'max': np.max(js_divergences)
    }
    
    print(f"完成 {len(js_divergences)}/{n_trials} 次有效试验。")
    print(f"  - 平均JS散度: {stats['mean']:.4f}")
    print(f"  - JS散度标准差: {stats['std']:.4f} (越小越稳定)")
    print(f"  - 范围: [{stats['min']:.4f}, {stats['max']:.4f}]")

    return {
        'values': js_divergences,
        'stats': stats
    }

def plot_skewness_js_relationship(all_results):
    """
    绘制偏度差异与JS散度关系的综合图表
    """
    skewness_pairs = sorted(all_results.keys())
    means = [all_results[pair]['mean'] for pair in skewness_pairs]
    stds = [all_results[pair]['std'] for pair in skewness_pairs]

    # 计算偏度差异（绝对值）
    skewness_diffs = [abs(pair[0] - pair[1]) for pair in skewness_pairs]

    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('不同偏峰分布对JS散度影响的综合分析', fontsize=16, fontweight='bold')

    # 图1: 偏度差异 vs 平均JS散度
    ax1.plot(skewness_diffs, means, 'bo-', linewidth=2, markersize=8, label='平均JS散度')
    ax1.fill_between(skewness_diffs,
                     [m - s for m, s in zip(means, stds)],
                     [m + s for m, s in zip(means, stds)],
                     alpha=0.3, label='±1标准差')
    ax1.set_xlabel('偏度差异 |a₁ - a₂|')
    ax1.set_ylabel('平均JS散度')
    ax1.set_title('偏度差异与平均JS散度的关系')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 图2: 偏度差异 vs JS散度标准差（稳定性）
    ax2.plot(skewness_diffs, stds, 'ro-', linewidth=2, markersize=8, label='JS散度标准差')
    ax2.set_xlabel('偏度差异 |a₁ - a₂|')
    ax2.set_ylabel('JS散度标准差')
    ax2.set_title('偏度差异与JS散度稳定性的关系')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 图3: 散点图 - 偏度差异 vs 平均JS散度
    scatter = ax3.scatter(skewness_diffs, means, c=stds, cmap='viridis', s=100, alpha=0.7)
    ax3.set_xlabel('偏度差异 |a₁ - a₂|')
    ax3.set_ylabel('平均JS散度')
    ax3.set_title('散点图：颜色表示稳定性')
    cbar = plt.colorbar(scatter, ax=ax3)
    cbar.set_label('JS散度标准差')
    ax3.grid(True, alpha=0.3)

    # 图4: 热力图风格的数据表
    ax4.axis('tight')
    ax4.axis('off')
    table_data = []
    for i, pair in enumerate(skewness_pairs):
        stats = all_results[pair]
        table_data.append([f'({pair[0]:.1f}, {pair[1]:.1f})', f'{skewness_diffs[i]:.2f}',
                          f'{stats["mean"]:.4f}', f'{stats["std"]:.4f}',
                          f'{stats["min"]:.4f}', f'{stats["max"]:.4f}'])

    table = ax4.table(cellText=table_data,
                     colLabels=['偏度对(a₁,a₂)', '偏度差异', '平均JS散度', '标准差', '最小值', '最大值'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 1.5)
    ax4.set_title('详细统计数据表')

    plt.tight_layout()
    plt.show()

if __name__ == '__main__':
    # 定义要测试的偏度对比组合
    # 固定方差为1.0，测试不同偏度组合对JS散度的影响
    skewness_pairs_to_test = [
        (-2.0, 2.0),   # 强左偏 vs 强右偏
        (-1.5, 1.5),   # 中等左偏 vs 中等右偏
        (-1.0, 1.0),   # 轻度左偏 vs 轻度右偏
        (-0.5, 0.5),   # 微弱左偏 vs 微弱右偏
        (-1.0, 0.0),   # 左偏 vs 正态
        (0.0, 1.0),    # 正态 vs 右偏
        (-2.0, 0.0),   # 强左偏 vs 正态
        (0.0, 2.0),    # 正态 vs 强右偏
        (0.0, 0.0),    # 正态 vs 正态（基线对照）
    ]

    all_results = {}
    all_js_values = {}  # 存储所有JS散度值用于后续分析

    print("开始测试不同偏峰分布对JS散度的影响...")
    print("固定方差 σ² = 1.0")
    print("=" * 60)

    for skew_left, skew_right in skewness_pairs_to_test:
        result = run_js_skewness_sensitivity_experiment(
            skewness_left=skew_left,
            skewness_right=skew_right,
            variance=1.0,   # 固定方差为1
            n_samples=500,  # 样本量
            n_trials=20,    # 试验次数
            n_dim=1         # 一维特征
        )
        if result:
            all_results[(skew_left, skew_right)] = result['stats']
            all_js_values[(skew_left, skew_right)] = result['values']

    # --- 最终结果汇总与解读 ---
    print("\n\n========================================")
    print("      JS散度对偏峰分布敏感性实验总结      ")
    print("========================================")
    print("本实验展示了不同偏度组合下两个分布之间的JS散度。")
    print("固定方差 σ² = 1.0")
    print("\n偏度对(a₁,a₂) | 偏度差异 | 平均JS散度 | JS散度标准差 | 最小值   | 最大值")
    print("-------------|----------|------------|-------------|----------|----------")
    for pair in sorted(all_results.keys(), key=lambda x: abs(x[0] - x[1])):
        stats = all_results[pair]
        skew_diff = abs(pair[0] - pair[1])
        print(f"  ({pair[0]:4.1f},{pair[1]:4.1f}) |   {skew_diff:5.2f}   |   {stats['mean']:.4f}   |    {stats['std']:.4f}    |  {stats['min']:.4f}  |  {stats['max']:.4f}")

    # 绘制综合分析图
    if all_results:
        plot_skewness_js_relationship(all_results)

        # 额外分析：计算相关性
        skewness_diffs = [abs(pair[0] - pair[1]) for pair in all_results.keys()]
        means = [all_results[pair]['mean'] for pair in all_results.keys()]
        stds = [all_results[pair]['std'] for pair in all_results.keys()]

        # 计算皮尔逊相关系数
        corr_mean = np.corrcoef(skewness_diffs, means)[0, 1]
        corr_std = np.corrcoef(skewness_diffs, stds)[0, 1]

        print(f"\n相关性分析:")
        print(f"偏度差异与平均JS散度的相关系数: {corr_mean:.4f}")
        print(f"偏度差异与JS散度标准差的相关系数: {corr_std:.4f}")

        # 趋势分析
        print(f"\n趋势分析:")
        if corr_mean > 0.5:
            print("- 偏度差异增大时，JS散度显著增加")
        elif corr_mean < -0.5:
            print("- 偏度差异增大时，JS散度显著减少")
        else:
            print("- 偏度差异与JS散度之间无明显线性关系")

        if corr_std > 0.5:
            print("- 偏度差异增大时，JS散度的稳定性降低（标准差增大）")
        elif corr_std < -0.5:
            print("- 偏度差异增大时，JS散度的稳定性提高（标准差减小）")
        else:
            print("- 偏度差异与JS散度稳定性之间无明显线性关系")

        # 特殊情况分析
        baseline_pair = (0.0, 0.0)
        if baseline_pair in all_results:
            baseline_js = all_results[baseline_pair]['mean']
            print(f"\n特殊情况分析:")
            print(f"- 基线情况（正态 vs 正态）的JS散度: {baseline_js:.4f}")
            print("- 这代表了相同分布间由于采样随机性导致的JS散度基线值")

            # 找出JS散度最大的偏度组合
            max_js_pair = max(all_results.keys(), key=lambda x: all_results[x]['mean'])
            max_js_value = all_results[max_js_pair]['mean']
            print(f"- 最大JS散度出现在偏度对 {max_js_pair}: {max_js_value:.4f}")
            print(f"- 相对于基线的增幅: {(max_js_value/baseline_js - 1)*100:.1f}%")

