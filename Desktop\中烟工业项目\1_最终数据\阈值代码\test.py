import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from scipy.integrate import simps
from sklearn.neighbors import KernelDensity
from sklearn.model_selection import GridSearchCV
import warnings
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler,MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap
from scipy.spatial.distance import cdist
from sklearn.preprocessing import StandardScaler
from sklearn.manifold import trustworthiness, TSNE
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import pdist, squareform
from dcor import distance_correlation
import umap
from scipy.special import logsumexp
import os
from alibi_detect.cd import MMDDrift
from datetime import datetime
from datetime import datetime, timedelta
from scipy.stats import ks_2samp
from scipy.stats import skewnorm

# 设置中文字体 (请确保您的系统有该字体)
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei'
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"中文字体设置失败: {e}")

def optimized_joint_kde(data, n, n_folds=3):
    """
    优化带宽的多维核密度估计
    参数：
        data : 数据矩阵，形状为(n_samples, n)
        n : 数据维度
        n_folds : 交叉验证折数（默认3折平衡精度与速度）
    返回：
        kde : 优化带宽后的KDE模型
    """
    # 数据维度校验
    assert data.shape[1] == n, f"输入数据维度应为{n}维，当前维度{data.shape[1]}"
    
    n_samples, d = data.shape
    
    # 计算所有维度的标准差，并取平均
    std_dev = np.mean(np.std(data, axis=0))
    
    # 分位数差估计 s*
    q977 = np.percentile(data, 97.7, axis=0)
    q023 = np.percentile(data, 2.3, axis=0)
    s_star = np.mean((q977 - q023) / 4)  # 平均化多维情况
    
    # 选择 min(σ̂, s*) 并计算带宽
    sigma_star = min(std_dev, s_star)
    h_m_star = 1.03 * sigma_star * (n_samples ** (-1 / (d + 4)))
    
    # 构建动态搜索网格
    bandwidth_grid = np.linspace(h_m_star*0.5, h_m_star*2, 5)
    
    # 交叉验证网格搜索
    grid = GridSearchCV(KernelDensity(kernel='gaussian'),
                      {'bandwidth': bandwidth_grid},
                      cv=n_folds,
                      scoring=lambda est, X: est.score(X),
                      n_jobs=-1)
    grid.fit(data)
    return grid.best_estimator_

def compute_divergence(kde_p, kde_q, n_samples=10000):
    """
    严格数学实现的JS散度计算
    参数：
        kde_p : 第一个分布的KDE模型
        kde_q : 第二个分布的KDE模型
        n_samples : 蒙特卡洛采样点数(建议>=1e5)
    返回：
        js_divergence
    """
    # ================== JS散度计算 ==================
    # 从P分布采样
    samples_p = kde_p.sample(n_samples)
    log_p_p = kde_p.score_samples(samples_p)

    # 从Q分布采样
    samples_q = kde_q.sample(n_samples)
    log_q_q = kde_q.score_samples(samples_q)
    # 混合分布M计算
    def calc_log_m(samples):
        log_p = kde_p.score_samples(samples)
        log_q = kde_q.score_samples(samples)
        return logsumexp([log_p - np.log(2), log_q - np.log(2)], axis=0)

    # 计算JS散度
    log_m_p = calc_log_m(samples_p)
    log_m_q = calc_log_m(samples_q)

    js_p = np.mean(log_p_p - log_m_p)
    js_q = np.mean(log_q_q - log_m_q)
    js = 0.5 * (js_p + js_q)
    js = max(js, 0.0)  # 确保非负
    
    # 范围[0, 1]
    return js/ np.log(2) 



def run_js_variance_sensitivity_experiment(variance, n_samples=1000, n_trials=100, n_dim=2):
    """
    执行JS散度对方差敏感性的重复实验。

    该函数生成两个来自相同正态分布 N(0, variance) 的样本，
    然后计算它们之间的JS散度。重复此过程多次以评估稳定性和均值。

    Args:
        variance (float): 要测试的正态分布的方差 (b)。
        n_samples (int): 每个分布的样本数量。
        n_trials (int): 重复实验的次数。
        n_dim (int): 数据的维度。

    Returns:
        dict: 包含JS散度列表和统计摘要的字典。
    """
    print(f"\n--- 开始测试: 方差(b) = {variance:.2f} ---")
    
    js_divergences = []
    # 计算标准差，因为 np.random.normal 使用 standard deviation (scale)
    std_dev = np.sqrt(variance)
    
    for i in range(n_trials):
        # 1. 生成两个来自同一分布的随机样本
        # 这是实验的核心：理论上分布完全相同，但实际样本不同
        # dist_a_samples = np.random.normal(loc=0, scale=std_dev, size=(n_samples, n_dim))
        # dist_b_samples = np.random.normal(loc=0, scale=std_dev, size=(n_samples, n_dim))
        # 新代码：
        # 生成左偏峰分布（负偏度）和右偏峰分布（正偏度），均值和标准差相同
        skewness_left = -1   # 负值表示左偏（长尾在左侧）
        skewness_right = 0   # 正值表示右偏（长尾在右侧）

        # 使用skewnorm生成偏峰分布，loc=0保证均值为0，scale=std_dev控制标准差
        dist_a_samples = skewnorm.rvs(a=skewness_left, loc=0, scale=std_dev, size=(n_samples, n_dim))
        dist_b_samples = skewnorm.rvs(a=skewness_right, loc=0, scale=std_dev, size=(n_samples, n_dim))
        # 2. 调用您的方法进行KDE和JS散度计算
        try:
            kde_a = optimized_joint_kde(dist_a_samples, n=n_dim)
            kde_b = optimized_joint_kde(dist_b_samples, n=n_dim)
            js = compute_divergence(kde_a, kde_b)
            js_divergences.append(js)
        except Exception as e:
            print(f"  在第 {i+1} 次试验中发生错误: {e}")
            continue
            
    if not js_divergences:
        print("所有试验均失败，无法生成结果。")
        return None

    # 计算统计摘要
    stats = {
        'mean': np.mean(js_divergences),
        'std': np.std(js_divergences),
        'min': np.min(js_divergences),
        'max': np.max(js_divergences)
    }
    
    print(f"完成 {len(js_divergences)}/{n_trials} 次有效试验。")
    print(f"  - 平均JS散度: {stats['mean']:.4f}")
    print(f"  - JS散度标准差: {stats['std']:.4f} (越小越稳定)")

    # 3. 绘制您要求的稳定性图
    plt.figure(figsize=(12, 6))
    plt.plot(js_divergences, 'o-', alpha=0.6, label=f'方差={variance}')
    plt.axhline(stats['mean'], color='red', linestyle='--', label=f'平均值 = {stats["mean"]:.4f}')
    plt.title(f'JS散度稳定性测试 (100次重复, 方差 b = {variance})')
    plt.xlabel('实验次数')
    plt.ylabel('JS散度值')
    plt.legend()
    plt.grid(True)
    plt.show()
    
    return {
        'values': js_divergences,
        'stats': stats
    }

if __name__ == '__main__':
    # 定义要测试的一系列方差值
    # 从小方差（数据集中）到大方差（数据分散）
    variances_to_test = [1.0]
    
    all_results = {}

    for var in variances_to_test:
        result = run_js_variance_sensitivity_experiment(
            variance=var,
            n_samples=500,  # 样本量可以根据您的实际情况调整
            n_trials=10,
            n_dim=1         # 假设您处理的是二维特征
        )
        if result:
            all_results[var] = result['stats']

    # --- 最终结果汇总与解读 ---
    print("\n\n========================================")
    print("      JS散度对方差敏感性实验总结      ")
    print("========================================")
    print("本实验展示了对于理论上完全相同的分布，")
    print("仅因随机抽样导致的JS散度基线值（背景噪音）。")
    print("\n方差(b) | 平均JS散度 | JS散度标准差 (稳定性)")
    print("--------|------------|----------------------")
    for var, stats in all_results.items():
        print(f"  {var:<6.2f}|   {stats['mean']:.4f}   |        {stats['std']:.4f}")

