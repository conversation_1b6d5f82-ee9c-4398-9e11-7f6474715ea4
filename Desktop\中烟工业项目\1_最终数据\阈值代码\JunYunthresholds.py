import numpy as np
import pandas as pd
from scipy.stats import ks_2samp
import time
import numpy as np
import torch
from scipy.stats import ks_2samp # 保留以作对比
from alibi_detect.cd import MMDDrift
from sklearn.preprocessing import StandardScaler
import numpy as np
from scipy.stats import ks_2samp
import time
import matplotlib.pyplot as plt
import seaborn as sns

import numpy as np
import numpy as np

def calculate_thresholds_from_parameters(
    raw_cv_lower: float,
    raw_cv_upper: float,
    outlier_count_lower: int,
    outlier_count_upper: int,
    alpha: float = 0.5,
    cv_scale: float = 4.0,
    total_samples: int = 30
):
    """
    根据给定的参数直接计算不均匀度分数的上下限阈值。

    该函数不处理原始数据，而是接受预设的变异系数(CV)和异常点数量，
    并应用与 `calculate_inhomogeneity_score` 相同的后续处理逻辑
    （IQR比例缩放、CV归一化、加权求和），以确定最终的阈值分数。

    Args:
        raw_cv_lower (float): 定义“下限”（均匀状态）的原始变异系数值。
        raw_cv_upper (float): 定义“上限”（不均匀状态）的原始变异系数值。
        outlier_count_lower (int): 定义“下限”的IQR异常点数量。
        outlier_count_upper (int): 定义“上限”的IQR异常点数量。
        alpha (float, optional): 权重。默认为 0.5。
        cv_scale (float, optional): Sigmoid函数的缩放/陡峭参数。默认为 4.0。
        total_samples (int, optional): 样本总数，用于计算IQR比例。默认为 30。

    Returns:
        dict: 一个包含 'lower_threshold' 和 'upper_threshold' 键的字典。
    """

    def _compute_score_from_params(raw_cv, outlier_count):
        """辅助函数：从参数计算单个总分。"""
        # --- 1. 处理 IQR 部分 ---
        iqr_ratio = outlier_count / total_samples
        # 将 [0, 0.5) 的范围映射到 [0, 1.0)
        scaled_iqr_ratio = min(1.0, iqr_ratio * 2)

        # --- 2. 处理 CV 部分 ---
        if raw_cv > 700 / cv_scale:
            normalized_cv = 1.0
        else:
            sigmoid_val = 1 / (1 + np.exp(-cv_scale * raw_cv))
            normalized_cv = 2 * sigmoid_val - 1

        # --- 3. 加权计算总分 ---
        total_score = alpha * scaled_iqr_ratio + (1 - alpha) * normalized_cv
        return total_score

    # --- 计算下限阈值 ---
    lower_threshold = _compute_score_from_params(raw_cv_lower, outlier_count_lower)
    
    # --- 计算上限阈值 ---
    upper_threshold = _compute_score_from_params(raw_cv_upper, outlier_count_upper)
    
    return {
        'lower_threshold': round(lower_threshold, 4),
        'upper_threshold': round(upper_threshold, 4)
    }

if __name__ == '__main__':
    # --- 定义您对“好”数据和“坏”数据的预期 ---
    
    # 定义“下限” (非常均匀的数据)
    # 我们认为，非常均匀的数据，其原始CV应该很小，比如0.05，并且没有IQR异常点。
    raw_cv_lower_limit = 0.015
    outlier_count_lower_limit = 0.5

    # 定义“上限” (我们认为需要警示的不均匀数据)
    # 我们认为，需要警示的数据，其原始CV可能达到了0.4，或者出现了3个以上的异常点。
    raw_cv_upper_limit = 0.05
    outlier_count_upper_limit = 1

    # 定义计算参数 (应与您实际使用的 `calculate_inhomogeneity_score` 函数保持一致)
    ALPHA = 0.4
    CV_SCALE = 4.0

    print("--- 阈值计算器 ---")
    print(f"计算参数: alpha={ALPHA}, cv_scale={CV_SCALE}\n")
    print(f"下限定义: CV={raw_cv_lower_limit}, 异常点数={outlier_count_lower_limit}")
    print(f"上限定义: CV={raw_cv_upper_limit}, 异常点数={outlier_count_upper_limit}")
    
    # --- 调用函数计算阈值 ---
    thresholds = calculate_thresholds_from_parameters(
        raw_cv_lower=raw_cv_lower_limit,
        raw_cv_upper=raw_cv_upper_limit,
        outlier_count_lower=outlier_count_lower_limit,
        outlier_count_upper=outlier_count_upper_limit,
        alpha=ALPHA,
        cv_scale=CV_SCALE
    )

    print("\n--- 计算结果 ---")
    print(f"计算出的下限阈值为: {thresholds['lower_threshold']}")
    print(f"计算出的上限阈值为: {thresholds['upper_threshold']}")

    print("\n--- 如何使用这些阈值 ---")
    print(f"当实际分数 < {thresholds['lower_threshold']} 时，可认为数据非常均匀 (通过)。")
    print(f"当分数 > {thresholds['upper_threshold']} 时，可认为数据不均匀 (警示)。")
    print(f"当分数介于两者之间时，可认为是过渡区域或需要关注。")


