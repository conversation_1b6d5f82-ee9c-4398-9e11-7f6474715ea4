import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from scipy.integrate import simps
from sklearn.neighbors import KernelDensity
from sklearn.model_selection import GridSearchCV
import warnings
from scipy.special import logsumexp
import os

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei'
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"中文字体设置失败: {e}")

def optimized_joint_kde(data, n, n_folds=3):
    """
    优化带宽的多维核密度估计
    """
    assert data.shape[1] == n, f"输入数据维度应为{n}维，当前维度{data.shape[1]}"
    
    n_samples, d = data.shape
    
    # 计算所有维度的标准差，并取平均
    std_dev = np.mean(np.std(data, axis=0))
    
    # 分位数差估计 s*
    q977 = np.percentile(data, 97.7, axis=0)
    q023 = np.percentile(data, 2.3, axis=0)
    s_star = np.mean((q977 - q023) / 4)  # 平均化多维情况
    
    # 选择 min(σ̂, s*) 并计算带宽
    sigma_star = min(std_dev, s_star)
    h_m_star = 1.03 * sigma_star * (n_samples ** (-1 / (d + 4)))
    
    # 构建动态搜索网格
    bandwidth_grid = np.linspace(h_m_star*0.5, h_m_star*2, 5)
    
    # 交叉验证网格搜索
    grid = GridSearchCV(KernelDensity(kernel='gaussian'),
                      {'bandwidth': bandwidth_grid},
                      cv=n_folds,
                      scoring=lambda est, X: est.score(X),
                      n_jobs=-1)
    grid.fit(data)
    return grid.best_estimator_

def compute_divergence(kde_p, kde_q, n_samples=10000):
    """
    严格数学实现的JS散度计算
    """
    # 从P分布采样
    samples_p = kde_p.sample(n_samples)
    log_p_p = kde_p.score_samples(samples_p)

    # 从Q分布采样
    samples_q = kde_q.sample(n_samples)
    log_q_q = kde_q.score_samples(samples_q)
    
    # 混合分布M计算
    def calc_log_m(samples):
        log_p = kde_p.score_samples(samples)
        log_q = kde_q.score_samples(samples)
        return logsumexp([log_p - np.log(2), log_q - np.log(2)], axis=0)

    # 计算JS散度
    log_m_p = calc_log_m(samples_p)
    log_m_q = calc_log_m(samples_q)

    js_p = np.mean(log_p_p - log_m_p)
    js_q = np.mean(log_q_q - log_m_q)
    js = 0.5 * (js_p + js_q)
    js = max(js, 0.0)  # 确保非负
    
    # 范围[0, 1]
    return js / np.log(2) 

def run_mean_ratio_experiment(base_mean=1.0, ratio_range=(0.1, 5.0), num_ratios=20, 
                             variance=1.0, n_samples=500, n_trials=15, n_dim=1):
    """
    探索均值变化比例对JS散度的影响
    
    Args:
        base_mean (float): 基准均值
        ratio_range (tuple): 比例范围 (最小比例, 最大比例)
        num_ratios (int): 测试的比例数量
        variance (float): 固定方差
        n_samples (int): 每个分布的样本数量
        n_trials (int): 重复实验次数
        n_dim (int): 数据维度
    
    Returns:
        dict: 包含比例和对应JS散度统计的字典
    """
    print(f"探索均值变化比例对JS散度的影响")
    print(f"基准均值: {base_mean}, 固定方差: {variance}")
    print(f"比例范围: {ratio_range[0]:.1f} - {ratio_range[1]:.1f}")
    print("=" * 60)
    
    # 生成比例序列（对数空间分布，更好地覆盖不同量级）
    ratios = np.logspace(np.log10(ratio_range[0]), np.log10(ratio_range[1]), num_ratios)
    
    results = {}
    std_dev = np.sqrt(variance)
    
    for i, ratio in enumerate(ratios):
        print(f"\n--- 测试比例 {i+1}/{num_ratios}: {ratio:.3f} ---")
        
        # 计算两个分布的均值
        mean_a = base_mean
        mean_b = base_mean * ratio
        
        js_divergences = []
        
        for trial in range(n_trials):
            # 生成两个不同均值的正态分布样本
            dist_a_samples = np.random.normal(loc=mean_a, scale=std_dev, size=(n_samples, n_dim))
            dist_b_samples = np.random.normal(loc=mean_b, scale=std_dev, size=(n_samples, n_dim))
            
            try:
                kde_a = optimized_joint_kde(dist_a_samples, n=n_dim)
                kde_b = optimized_joint_kde(dist_b_samples, n=n_dim)
                js = compute_divergence(kde_a, kde_b)
                js_divergences.append(js)
            except Exception as e:
                print(f"  试验 {trial+1} 发生错误: {e}")
                continue
        
        if js_divergences:
            stats = {
                'mean': np.mean(js_divergences),
                'std': np.std(js_divergences),
                'min': np.min(js_divergences),
                'max': np.max(js_divergences),
                'mean_a': mean_a,
                'mean_b': mean_b,
                'mean_diff': abs(mean_b - mean_a),
                'mean_ratio': ratio
            }
            results[ratio] = stats
            
            print(f"  均值对: ({mean_a:.2f}, {mean_b:.2f})")
            print(f"  平均JS散度: {stats['mean']:.4f} ± {stats['std']:.4f}")
        else:
            print(f"  比例 {ratio:.3f} 的所有试验均失败")
    
    return results

def plot_mean_ratio_analysis(results, save_path="mean_ratio_js_analysis.png"):
    """
    绘制均值比例与JS散度关系的分析图
    """
    if not results:
        print("没有结果可以绘制")
        return
    
    # 提取数据
    ratios = sorted(results.keys())
    js_means = [results[r]['mean'] for r in ratios]
    js_stds = [results[r]['std'] for r in ratios]
    mean_diffs = [results[r]['mean_diff'] for r in ratios]
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('均值变化比例对JS散度影响的综合分析', fontsize=16, fontweight='bold')
    
    # 图1: 均值比例 vs JS散度
    ax1.semilogx(ratios, js_means, 'bo-', linewidth=2, markersize=6, label='平均JS散度')
    ax1.fill_between(ratios, 
                     [m - s for m, s in zip(js_means, js_stds)], 
                     [m + s for m, s in zip(js_means, js_stds)], 
                     alpha=0.3, label='±1标准差')
    ax1.axvline(x=1.0, color='red', linestyle='--', alpha=0.7, label='比例=1 (相同均值)')
    ax1.set_xlabel('均值比例 (μ₂/μ₁)')
    ax1.set_ylabel('JS散度')
    ax1.set_title('均值比例与JS散度的关系')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 图2: 均值差异 vs JS散度
    ax2.plot(mean_diffs, js_means, 'go-', linewidth=2, markersize=6, label='平均JS散度')
    ax2.fill_between(mean_diffs, 
                     [m - s for m, s in zip(js_means, js_stds)], 
                     [m + s for m, s in zip(js_means, js_stds)], 
                     alpha=0.3, label='±1标准差')
    ax2.set_xlabel('均值差异 |μ₂ - μ₁|')
    ax2.set_ylabel('JS散度')
    ax2.set_title('均值差异与JS散度的关系')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 图3: 散点图 - 比例 vs JS散度，颜色表示稳定性
    scatter = ax3.scatter(ratios, js_means, c=js_stds, cmap='viridis', s=80, alpha=0.7)
    ax3.set_xscale('log')
    ax3.axvline(x=1.0, color='red', linestyle='--', alpha=0.7)
    ax3.set_xlabel('均值比例 (μ₂/μ₁)')
    ax3.set_ylabel('JS散度')
    ax3.set_title('散点图：颜色表示JS散度稳定性')
    cbar = plt.colorbar(scatter, ax=ax3)
    cbar.set_label('JS散度标准差')
    ax3.grid(True, alpha=0.3)
    
    # 图4: 数据表
    ax4.axis('tight')
    ax4.axis('off')
    
    # 选择一些关键比例点显示在表格中
    key_indices = [0, len(ratios)//4, len(ratios)//2, 3*len(ratios)//4, len(ratios)-1]
    table_data = []
    for i in key_indices:
        if i < len(ratios):
            r = ratios[i]
            stats = results[r]
            table_data.append([
                f'{r:.3f}',
                f'{stats["mean_a"]:.2f}',
                f'{stats["mean_b"]:.2f}',
                f'{stats["mean_diff"]:.2f}',
                f'{stats["mean"]:.4f}',
                f'{stats["std"]:.4f}'
            ])
    
    table = ax4.table(cellText=table_data,
                     colLabels=['比例', 'μ₁', 'μ₂', '|μ₂-μ₁|', 'JS散度', '标准差'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.8)
    ax4.set_title('关键数据点统计表')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"分析图已保存到: {save_path}")
    plt.show()

if __name__ == '__main__':
    print("均值变化比例对JS散度影响的探索性分析")
    print("=" * 60)
    
    # 运行实验
    results = run_mean_ratio_experiment(
        base_mean=1.0,          # 基准均值
        ratio_range=(0.1, 10.0), # 比例范围：0.1倍到10倍
        num_ratios=25,          # 测试25个不同比例
        variance=1.0,           # 固定方差
        n_samples=500,          # 每个分布500个样本
        n_trials=10,            # 每个比例重复10次
        n_dim=1                 # 一维数据
    )
    
    # 绘制分析图
    if results:
        plot_mean_ratio_analysis(results)
        
        # 统计分析
        print("\n" + "=" * 60)
        print("统计分析结果")
        print("=" * 60)
        
        ratios = sorted(results.keys())
        js_values = [results[r]['mean'] for r in ratios]
        
        # 找到JS散度的最大值和最小值
        max_js_ratio = ratios[np.argmax(js_values)]
        min_js_ratio = ratios[np.argmin(js_values)]
        
        print(f"JS散度最大值出现在比例: {max_js_ratio:.3f}, JS散度: {max(js_values):.4f}")
        print(f"JS散度最小值出现在比例: {min_js_ratio:.3f}, JS散度: {min(js_values):.4f}")
        
        # 分析对称性
        ratio_1_idx = np.argmin([abs(r - 1.0) for r in ratios])
        baseline_js = js_values[ratio_1_idx]
        print(f"基线JS散度 (比例≈1): {baseline_js:.4f}")
        
        # 计算相关性
        log_ratios = np.log10(ratios)
        corr_log = np.corrcoef(log_ratios, js_values)[0, 1]
        print(f"对数比例与JS散度的相关系数: {corr_log:.4f}")
        
        print("\n实验完成！")
    else:
        print("实验失败，没有获得有效结果。")
